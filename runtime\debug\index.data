a:59:{s:13:"6888edd7b0ef8";a:13:{s:3:"tag";s:13:"6888edd7b0ef8";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804247.58354;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10357088;s:14:"processingTime";d:0.2347559928894043;}s:13:"6888ee6027413";a:13:{s:3:"tag";s:13:"6888ee6027413";s:3:"url";s:34:"http://silver/backend/worker/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804383.978498;s:10:"statusCode";i:200;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10933680;s:14:"processingTime";d:0.2593247890472412;}s:13:"6888ef1e636dc";a:13:{s:3:"tag";s:13:"6888ef1e636dc";s:3:"url";s:42:"http://silver/backend/archive-worker/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804574.264232;s:10:"statusCode";i:200;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9920312;s:14:"processingTime";d:0.2255239486694336;}s:13:"6888ef20d9f85";a:13:{s:3:"tag";s:13:"6888ef20d9f85";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804576.795717;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11500752;s:14:"processingTime";d:0.1932508945465088;}s:13:"6888ef4af38bb";a:13:{s:3:"tag";s:13:"6888ef4af38bb";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804618.772582;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.2658050060272217;}s:13:"6888ef604ea0a";a:13:{s:3:"tag";s:13:"6888ef604ea0a";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804640.12266;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.2566850185394287;}s:13:"6888f05a34e36";a:13:{s:3:"tag";s:13:"6888f05a34e36";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804889.7536;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10504720;s:14:"processingTime";d:0.46587491035461426;}s:13:"6888f0895d0e7";a:13:{s:3:"tag";s:13:"6888f0895d0e7";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753804937.270454;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10357088;s:14:"processingTime";d:0.2531430721282959;}s:13:"6888f239a7da2";a:13:{s:3:"tag";s:13:"6888f239a7da2";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805369.405878;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11505192;s:14:"processingTime";d:0.399716854095459;}s:13:"6888f23d49687";a:13:{s:3:"tag";s:13:"6888f23d49687";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805373.186065;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10509584;s:14:"processingTime";d:0.17670798301696777;}s:13:"6888f24710e45";a:13:{s:3:"tag";s:13:"6888f24710e45";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805382.94858;s:10:"statusCode";i:200;s:8:"sqlCount";i:81;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10438592;s:14:"processingTime";d:0.24302077293395996;}s:13:"6888f2ffc16b4";a:13:{s:3:"tag";s:13:"6888f2ffc16b4";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805567.58131;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11502016;s:14:"processingTime";d:0.27893900871276855;}s:13:"6888f303172b6";a:13:{s:3:"tag";s:13:"6888f303172b6";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805570.977544;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10506024;s:14:"processingTime";d:0.20102810859680176;}s:13:"6888f30bb6948";a:13:{s:3:"tag";s:13:"6888f30bb6948";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805579.636106;s:10:"statusCode";i:200;s:8:"sqlCount";i:119;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10751152;s:14:"processingTime";d:0.28680992126464844;}s:13:"6888f30c23e1c";a:13:{s:3:"tag";s:13:"6888f30c23e1c";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805579.982867;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10968800;s:14:"processingTime";d:0.2603578567504883;}s:13:"6888f30c59fc5";a:13:{s:3:"tag";s:13:"6888f30c59fc5";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805579.960045;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10968800;s:14:"processingTime";d:0.4951648712158203;}s:13:"6888f31b1f1bc";a:13:{s:3:"tag";s:13:"6888f31b1f1bc";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805594.981167;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11502208;s:14:"processingTime";d:0.2749500274658203;}s:13:"6888f3928a397";a:13:{s:3:"tag";s:13:"6888f3928a397";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805714.436409;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10512024;s:14:"processingTime";d:0.2074880599975586;}s:13:"6888f39c88e2e";a:13:{s:3:"tag";s:13:"6888f39c88e2e";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805724.424201;s:10:"statusCode";i:200;s:8:"sqlCount";i:130;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10820944;s:14:"processingTime";d:0.289888858795166;}s:13:"6888f39ce5aa6";a:13:{s:3:"tag";s:13:"6888f39ce5aa6";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805724.753097;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10967576;s:14:"processingTime";d:0.2627079486846924;}s:13:"6888f39d217d5";a:13:{s:3:"tag";s:13:"6888f39d217d5";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805724.773294;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10967576;s:14:"processingTime";d:0.4568600654602051;}s:13:"6888f3a619ec2";a:13:{s:3:"tag";s:13:"6888f3a619ec2";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805733.981207;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10512024;s:14:"processingTime";d:0.2080678939819336;}s:13:"6888f3af7f58b";a:13:{s:3:"tag";s:13:"6888f3af7f58b";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805743.397598;s:10:"statusCode";i:200;s:8:"sqlCount";i:130;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10820944;s:14:"processingTime";d:0.27861499786376953;}s:13:"6888f3afde58a";a:13:{s:3:"tag";s:13:"6888f3afde58a";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805743.711604;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10967576;s:14:"processingTime";d:0.23813700675964355;}s:13:"6888f3b011547";a:13:{s:3:"tag";s:13:"6888f3b011547";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805743.741912;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10967576;s:14:"processingTime";d:0.4113578796386719;}s:13:"6888f3b04415b";a:13:{s:3:"tag";s:13:"6888f3b04415b";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805743.74701;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10967576;s:14:"processingTime";d:0.6261780261993408;}s:13:"6888f3b8057ce";a:13:{s:3:"tag";s:13:"6888f3b8057ce";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805751.831944;s:10:"statusCode";i:200;s:8:"sqlCount";i:24;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10513248;s:14:"processingTime";d:0.24462008476257324;}s:13:"6888f3c47c6a7";a:13:{s:3:"tag";s:13:"6888f3c47c6a7";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.375132;s:10:"statusCode";i:200;s:8:"sqlCount";i:150;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10999496;s:14:"processingTime";d:0.27462100982666016;}s:13:"6888f3c4d1535";a:13:{s:3:"tag";s:13:"6888f3c4d1535";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.720568;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:0.2669239044189453;}s:13:"6888f3c51539a";a:13:{s:3:"tag";s:13:"6888f3c51539a";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.734595;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:0.43235087394714355;}s:13:"6888f3c545b89";a:13:{s:3:"tag";s:13:"6888f3c545b89";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.74815;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:0.6209921836853027;}s:13:"6888f3c576b49";a:13:{s:3:"tag";s:13:"6888f3c576b49";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.704201;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:0.8621668815612793;}s:13:"6888f3c5a9b04";a:13:{s:3:"tag";s:13:"6888f3c5a9b04";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.766381;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:0.9812378883361816;}s:13:"6888f3c5d859d";a:13:{s:3:"tag";s:13:"6888f3c5d859d";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.784706;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:1.166821002960205;}s:13:"6888f3c6143cc";a:13:{s:3:"tag";s:13:"6888f3c6143cc";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805764.851374;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969592;s:14:"processingTime";d:1.317126989364624;}s:13:"6888f3cf77e03";a:13:{s:3:"tag";s:13:"6888f3cf77e03";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805775.374625;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11503600;s:14:"processingTime";d:0.22526001930236816;}s:13:"6888f3d25fba0";a:13:{s:3:"tag";s:13:"6888f3d25fba0";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805778.284761;s:10:"statusCode";i:200;s:8:"sqlCount";i:25;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10520000;s:14:"processingTime";d:0.1880509853363037;}s:13:"6888f3db4214e";a:13:{s:3:"tag";s:13:"6888f3db4214e";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805787.169094;s:10:"statusCode";i:200;s:8:"sqlCount";i:183;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11328968;s:14:"processingTime";d:0.2759890556335449;}s:13:"6888f3db9781a";a:13:{s:3:"tag";s:13:"6888f3db9781a";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805787.493466;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969120;s:14:"processingTime";d:0.22054195404052734;}s:13:"6888f3dbc997a";a:13:{s:3:"tag";s:13:"6888f3dbc997a";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805787.47819;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969120;s:14:"processingTime";d:0.40241503715515137;}s:13:"6888f3ded01d7";a:13:{s:3:"tag";s:13:"6888f3ded01d7";s:3:"url";s:69:"http://silver/backend/worker-payment/edit?worker_id=108&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805790.716645;s:10:"statusCode";i:200;s:8:"sqlCount";i:26;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10527976;s:14:"processingTime";d:0.22629499435424805;}s:13:"6888f3ea5fbfd";a:13:{s:3:"tag";s:13:"6888f3ea5fbfd";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805802.283271;s:10:"statusCode";i:200;s:8:"sqlCount";i:200;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11465624;s:14:"processingTime";d:0.2566821575164795;}s:13:"6888f3eab96b2";a:13:{s:3:"tag";s:13:"6888f3eab96b2";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805802.579189;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969160;s:14:"processingTime";d:0.2525038719177246;}s:13:"6888f3eae8798";a:13:{s:3:"tag";s:13:"6888f3eae8798";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805802.606003;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969160;s:14:"processingTime";d:0.4277608394622803;}s:13:"6888f3eb1fd27";a:13:{s:3:"tag";s:13:"6888f3eb1fd27";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805802.612052;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10969160;s:14:"processingTime";d:0.5614540576934814;}s:13:"6888f4593af98";a:13:{s:3:"tag";s:13:"6888f4593af98";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805913.09419;s:10:"statusCode";i:200;s:8:"sqlCount";i:95;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11503792;s:14:"processingTime";d:0.26334500312805176;}s:13:"6888f48204e65";a:13:{s:3:"tag";s:13:"6888f48204e65";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753805953.933315;s:10:"statusCode";i:200;s:8:"sqlCount";i:1592;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:37133192;s:14:"processingTime";d:1.420238971710205;}s:13:"6888f94199d41";a:13:{s:3:"tag";s:13:"6888f94199d41";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807169.324474;s:10:"statusCode";i:200;s:8:"sqlCount";i:1592;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:37179488;s:14:"processingTime";d:2.688628911972046;}s:13:"6888f9472b09c";a:13:{s:3:"tag";s:13:"6888f9472b09c";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807174.970643;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11325168;s:14:"processingTime";d:0.30111098289489746;}s:13:"6888f9481f4fe";a:13:{s:3:"tag";s:13:"6888f9481f4fe";s:3:"url";s:98:"http://silver/backend/invoice/get-products-block-sizes?ids=7%2C9%2C8%2C11%2C10%2C13%2C12%2C14%2C15";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807175.865776;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9292312;s:14:"processingTime";d:0.2963688373565674;}s:13:"6888f9508bbc7";a:13:{s:3:"tag";s:13:"6888f9508bbc7";s:3:"url";s:55:"http://silver/backend/invoice/get-drivers?client_id=371";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807184.248555;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9340840;s:14:"processingTime";d:0.3431720733642578;}s:13:"6888f950b2a40";a:13:{s:3:"tag";s:13:"6888f950b2a40";s:3:"url";s:54:"http://silver/backend/invoice/get-prices?client_id=371";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807184.252278;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9383224;s:14:"processingTime";d:0.5280539989471436;}s:13:"6888f9b0e76e5";a:13:{s:3:"tag";s:13:"6888f9b0e76e5";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807280.728247;s:10:"statusCode";i:200;s:8:"sqlCount";i:194;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11322336;s:14:"processingTime";d:0.6024959087371826;}s:13:"6888f9b1a7412";a:13:{s:3:"tag";s:13:"6888f9b1a7412";s:3:"url";s:62:"http://silver/backend/invoice/index?_pjax=%23invoice-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807281.401364;s:10:"statusCode";i:200;s:8:"sqlCount";i:1593;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:32741736;s:14:"processingTime";d:3.698076009750366;}s:13:"6888f9b53f34e";a:13:{s:3:"tag";s:13:"6888f9b53f34e";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807282.066812;s:10:"statusCode";i:200;s:8:"sqlCount";i:1593;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:37166136;s:14:"processingTime";d:6.278336048126221;}s:13:"6888f9c1ecf61";a:13:{s:3:"tag";s:13:"6888f9c1ecf61";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807297.765555;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11325168;s:14:"processingTime";d:0.29005002975463867;}s:13:"6888f9c2cff49";a:13:{s:3:"tag";s:13:"6888f9c2cff49";s:3:"url";s:98:"http://silver/backend/invoice/get-products-block-sizes?ids=7%2C9%2C8%2C11%2C10%2C13%2C12%2C14%2C15";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807298.634377;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9292312;s:14:"processingTime";d:0.23566484451293945;}s:13:"6888f9c7307e7";a:13:{s:3:"tag";s:13:"6888f9c7307e7";s:3:"url";s:55:"http://silver/backend/invoice/get-drivers?client_id=371";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807302.995823;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9340840;s:14:"processingTime";d:0.2201230525970459;}s:13:"6888f9c756870";a:13:{s:3:"tag";s:13:"6888f9c756870";s:3:"url";s:54:"http://silver/backend/invoice/get-prices?client_id=371";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753807302.997354;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9383224;s:14:"processingTime";d:0.39479804039001465;}}